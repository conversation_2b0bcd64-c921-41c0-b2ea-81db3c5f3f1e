import API from './Api';

// 获取所有课程事件 GET /api/course-events
interface GetAllCourseEvents extends API.PagiNation<API.CourseEvent> {
  date_start: any;
  date_end: any;
  categories?: any;
}
export async function getAllCourseEvents(params?: GetAllCourseEvents) {
  return API.get('/course-events', {
    params,
  });
}

// 获取单个课程事件 GET /api/course-events
interface GetCourseEventById {
  courseEventId: API.ID;
}
export async function getCourseEventById(params?: GetCourseEventById) {
  return API.get('/course-events/' + params?.courseEventId);
}

// 创建课程事件 POST /api/course-events
interface CreateCourseEventAuto {
  category: string[][];
  name: string;
  teacher: API.ID;
  zoom_account?: string;
  note?: string;
  first_date: Date;
  count: number;
  week_days: number[];
  week_times_start: string[];
  week_times_end: string[];
  durations: number[];
}
export async function createCourseEventAuto(params: CreateCourseEventAuto) {
  return API.post('/course-events/auto', {
    data: {
      ...params,
    },
  });
}

// 批量创建课程事件 POST /api/course-events/bulk
interface CourseDate {
  date: Date;
  start_date_str: string;
  end_date_str: string;
  hours: number;
}

interface CreateCourseEventBulk {
  category: string[][];
  name: string;
  teacher: API.ID;
  check_user?: API.ID;
  department_ids?: number[];
  note?: string;
  work_content?: string;
  zoom_account?: string;
  course_dates: CourseDate[];
  recurrence_rule?: API.RecurrenceRule; // Include recurrence rule for duplication
}

export async function createCourseEventBulk(params: CreateCourseEventBulk) {
  return API.post('/course-events/bulk', {
    data: {
      ...params,
    },
  });
}

// 更新课程事件 PATCH /api/course-events/:id
interface UpdateCourseEvent extends Partial<API.CourseEvent> {
  courseEventId: API.ID;
}
export async function updateCourseEvent(params: UpdateCourseEvent) {
  return API.patch('/course-events/' + params.courseEventId, {
    data: {
      ...params,
    },
  });
}

// 删除课程事件 DELETE /api/course-events/:id
interface DeleteCourseEventById {
  courseEventId: API.ID;
}
export async function deleteCourseEventById(params: DeleteCourseEventById) {
  return API.delete('/course-events/' + params.courseEventId);
}

// 批量更新课程事件 PATCH /api/course-events/:id
interface UpdateMultiCourseEvent extends Partial<API.CourseEvent> {
  ids: API.ID[];
}
export async function updateMultiCourseEvent(params: UpdateMultiCourseEvent) {
  return API.post('/course-events/multi-update', {
    data: {
      ...params,
    },
  });
}

// 完成课程事件 DELETE /api/course-events/:id
interface FinishCourseEventById {
  courseEventId: API.ID;
}
export async function finishCourseEventById(params: FinishCourseEventById) {
  return API.post('/course-events/finish/' + params.courseEventId);
}

// 切换课程事件通知状态 PATCH /api/course-events/:id/toggle-notification
interface ToggleCourseEventNotification {
  courseEventId: API.ID;
}
export async function toggleCourseEventNotification(params: ToggleCourseEventNotification) {
  return API.patch('/course-events/' + params.courseEventId + '/toggle-notification');
}
