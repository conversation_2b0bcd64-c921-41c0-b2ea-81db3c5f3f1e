import { useBoolean, useRequest } from 'ahooks';
import { Button, Modal, message, Calendar } from 'antd';
import dayjs from 'dayjs';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
////
import { useUserOptions } from '@/hooks/useApiOptions';
import { genContractNo } from '@/services/request/student';
import { useParams } from 'umi';
import { formatterEn } from '@/utils/function';

export interface RenderJointProps {
  dataSource: {
    row: API.Course;
    data: API.Student;
  };
}

const renderData = (data: any) => (
  <span className="underline">{`\u2002\u2002\u2002\u2002${data || ''
    }\u2002\u2002\u2002\u2002`}</span>
);

const renderStrong = (data: any) => <span className="font-bold">{data}</span>;

const RenderJoint: React.FC<RenderJointProps> = (props) => {
  // store
  const [open, { setFalse, setTrue }] = useBoolean(false);
  const [loading, loadingAction] = useBoolean(false);
  const { studentId = '' } = useParams<any>();
  const { renderUserLabel } = useUserOptions();
  const { row, data } = props.dataSource;

  const genContractNoApi = useRequest(
    async () => await genContractNo({ studentId }),
    {
      manual: true,
    },
  );

  // action
  const handleOpen = async () => {
    try {
      await genContractNoApi.runAsync();
      setTrue();
    } catch (error: any) {
      console.log(error);
      message.error(error?.message || 'error');
    }
  };
  const handleDownload = async () => {
    loadingAction.setTrue();
    const doc = new jsPDF({
      orientation: 'p',
      unit: 'mm',
      format: [210, 297],
    });
    for (let i = 1; i < 6; i++) {
      let elem = document.getElementById('page1' + i) as HTMLDivElement;
      let canvas = await html2canvas(elem, { scale: 2 });
      let dataURI = canvas.toDataURL('image/jpeg');
      doc.addImage(dataURI, 'JPEG', 0.1, 0.1, 210, 297);
      if (i !== 6) doc.addPage();
    }
    doc.save(
      `就职合同【${genContractNoApi?.data?.contractNo}】${data?.name}.pdf`,
    );
    setFalse();
    loadingAction.setFalse();
  };

  return (
    <>
      <Modal
        width={1142}
        open={open}
        title="生成合同"
        onCancel={setFalse}
        okText="下载"
        okButtonProps={{
          loading,
        }}
        destroyOnClose
        onOk={handleDownload}
      >
        <div className="flex flex-col justify-center tracking-wide font-mono render-joint-watermark">
          <div
            id="page11"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·学部</span>
                <div className="render-joint-icon"></div>
              </div>
              <div>
                合同编码：
                <span className="underline">
                  {genContractNoApi?.data?.contractNo}
                </span>
              </div>
              <div>
                经办人：{renderData(renderUserLabel(row?.accept_teacher))}
                {/* &nbsp;&nbsp;&nbsp;&nbsp; 来源：{renderData(row?.custom_source)} */}
              </div>
              <div>
                对应人：{renderData(renderUserLabel(data?.sale_teacher1))}
              </div>
              <div className="text-center mt-10 text-4xl font-bold">
                新领域理工就职课程服务协议
              </div>
              <div className="mt-28 text-xl">
                <div className="mt-6 text-xl">
                  <span className="font-bold">甲方（本机构）：</span>
                  新领域理工塾(SHINRYOIKIRIKOJUKU)
                </div>
                <div className="mt-6 text-xl">联系电话：+81-0359565795</div>
                <div className="mt-6 text-xl">
                  地址：東京都豐島区池袋 1-7-18 Sunshine SCH ビル5F
                </div>
                <div className="mt-6 font-bold">
                  乙方（学员）: {renderData(data?.name)}
                </div>
                <div className="mt-6">
                  身份证号：{renderData(data?.CN_ID_no)}
                </div>
                <div className="mt-6">
                  在留卡号（如有）:{renderData(data?.JP_ID_no)}
                </div>
                <div className="mt-6">
                  手机号码：{renderData(data?.tel_jp || data?.tel_cn)}
                  &nbsp;&nbsp;&nbsp;&nbsp; 出身校：
                  {renderData(data?.lang_school)}
                </div>
                <div className="mt-6">地址：{renderData(data?.address)}</div>
                {/* <div className="mt-6">
                  乙方监护人/紧急联系人:{renderData(data?.emergency_name)}
                  &nbsp;&nbsp;&nbsp;&nbsp; 与联系人关系:
                  {renderData(data?.emergency_relationship)}
                </div>
                <div className="mt-6">
                  手机号码:{renderData(data?.emergency_tel)}
                  &nbsp;&nbsp;&nbsp;&nbsp; 微信号:
                  {renderData(data?.emergency_wechat)}
                </div> */}
                <div className="mt-8 leading-loose indent-8">
                  根据《中华人民共和国民法典》
                  <div className="w-0 inline-block" />
                  《中华人民共和国合同法》等有关法律法规的规定，甲、乙双方遵循平等、自愿、公平、诚实、守信的原则，经协商一致，签署本合同。
                </div>
              </div>
              <div className="mt-20">
                <div className="mt-6 text-lg font-bold">一、报名课程</div>
                <div className="mt-6 text-lg font-bold indent-8">
                  学员所报课程为：{renderData(row?.course_name)}
                </div>
                <div className="mt-6 text-lg font-bold indent-8">
                  课程有效期至：
                  {renderData(dayjs(row?.expired_date).format('YYYY年MM月DD'))}
                </div>
              </div>
              <div className="mt-20 text-lg font-bold">二、课程费用</div>
              <div className="mt-6 text-lg font-bold indent-8">
                课程总价:{renderData(formatterEn(row?.original_price))}
                &nbsp;&nbsp;&nbsp;&nbsp; 实缴金额:
                {renderData(formatterEn(row?.final_price))}
              </div>
              <div className="mt-6 text-lg font-bold indent-8">
                其他:{renderData(formatterEn(row?.off_price))}
              </div>
            </div>
          </div>

          <div
            id="page12"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·学部</span>
                <div className="render-joint-icon"></div>
              </div>


              <div className="mt-20 leading-8 indent-8">
                <div className="font-bold text-lg indent-0">
                  三、特别声明
                </div>
                <div className="indent-0">
                  1.	在申请入学手续时，为了甲方能更好地了解乙方信息，乙方必须填写真实、准确的个人信息；
                </div>
                <div className="indent-0">
                  2.	入学资格为在专门学校、短期大学、大学或大学院在读或毕业三年以内者，请填写甲方规定的本 {renderStrong('《新领域理工就职课程服务协议》')}并提交。非上述范围学员，服务内容会根据实际情况进行相关调整，经双方协商一致确定。
                </div>
                <div className="indent-0">
                  3.	乙方须充分了解私塾的办学理念、办学特色及有关政策规定，乙方保证其有能力支付按照协议规定所提供的教学课程的学费。同时，受讲资格仅属于合同签约的本人，不能将受讲资格转让或借给他人；
                </div>
                <div className="indent-0">
                  4.	在入学申请过程中涉及的所有材料，甲乙双方均负有保密义务，不得向无关的第三方透露。
                </div>
              </div>

              <div className="mt-6 font-bold text-lg indent-0">
                四、课程服务内容
              </div>
              <div className="indent-7">
                经双方协商，乙方同意接受甲方提供的以下日本就职指导课程和服务：
              </div>
              <div className="indent-0">
                1. 向乙方提供日本就职咨询、在日留学生日本就职概况、公司选考情报等信息，并为乙方制定就职方案提供相应的咨询及建议；                </div>
              <div className="indent-0">
                2. 向乙方介绍申请日本就职要求及申请程序，指导乙方准备就职的相关材料等服务；
              </div>
              <div className="indent-0">
                3.	向乙方提供日本就职相关的面试指导和个人面谈服务；
              </div>
              <div className="mt-6 font-bold text-lg indent-0">
                五、服务项目
              </div>
              <div className="font-bold text-lg indent-0">
                【5个月课程】
              </div>
              <div className="indent-0">
                （一）基础套餐课程：
              </div>
              <div className="indent-0">
                1.  日本就职相关讲义课程
              </div>
              <div className="indent-0">
                2.  日本就职网测辅导书类资源（合同期满/解除后，使用权限关闭）
              </div>
              <div className="indent-0">
                3.  大班课程共计：4次
              </div>
              <div className="indent-0">
                4.  无业界小班课程
              </div>
              <div className="indent-0">
                5.  1v1就职指导面谈/1v1就职模拟面试共计：5次
              </div>
              <div className="mt-5 indent-0">
                （二）标准套餐课程：
              </div>
              <div className="indent-0">
                1.  日本就职相关讲义课程
              </div>
              <div className="indent-0">
                2.  日本就职网测辅导书类资源（合同期满/解除后，使用权限关闭）
              </div>
              <div className="indent-0">
                3.  大班课程共计：4次
              </div>
              <div className="indent-0">
                4.  业界小班课程共计：5次
              </div>
              <div className="indent-0">
                5.  1v1就职指导面谈/1v1就职模拟面试共计：10次
              </div>
              <div className="mt-5 indent-0">
                （三）升级套餐课程：
              </div>
              <div className="indent-0">
                1.  日本就职相关讲义课程
              </div>
              <div className="indent-0">
                2.  日本就职网测辅导书类资源（合同期满/解除后，使用权限关闭）
              </div>
              <div className="indent-0">
                3.  大班课程共计：4次
              </div>
              <div className="indent-0">
                4.  业界小班课程共计：10次
              </div>
              <div className="indent-0">
                5.  1v1就职指导面谈/1v1就职模拟面试共计：15次
              </div>

              <div className="mt-5 indent-0">
                （四）附加课程内容（需额外付费）：
              </div>
              <div className="indent-0">
                代码测试服务（按次收费，每次10000日元）
              </div>
              <div className="mt-6 font-bold text-lg indent-0">
                【10个月课程】
              </div>
              <div className="indent-0">
                （一）基础套餐课程：
              </div>
              <div className="indent-0">
                1.  日本就职相关讲义课程
              </div>
              <div className="indent-0">
                2.  日本就职网测辅导书类资源（合同期满/解除后，使用权限关闭）
              </div>
              <div className="indent-0">
                3.  大班课程共计：4次
              </div>
              <div className="indent-0">
                4.  无业界小班课程
              </div>
              <div className="indent-0">
                5.  1v1就职指导面谈/1v1就职模拟面试共计：10次
              </div>


            </div>
          </div>

          <div
            id="page13"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·学部</span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-8">

                <div className="mt-0 indent-0">
                  （二）标准套餐课程：
                </div>
                <div className="indent-0">
                  1.  日本就职相关讲义课程
                </div>
                <div className="indent-0">
                  2.  日本就职网测辅导书类资源（合同期满/解除后，使用权限关闭）
                </div>
                <div className="indent-0">
                  3.  大班课程共计：4次
                </div>
                <div className="indent-0">
                  4.  业界小班课程共计：10次
                </div>
                <div className="indent-0">
                  5.  1v1就职指导面谈/1v1就职模拟面试共计：20次
                </div>
                <div className="mt-5 indent-0">
                  （三）升级套餐课程：
                </div>
                <div className="indent-0">
                  1.  日本就职相关讲义课程
                </div>
                <div className="indent-0">
                  2.  日本就职网测辅导书类资源（合同期满/解除后，使用权限关闭）
                </div>
                <div className="indent-0">
                  3.  大班课程共计：4次
                </div>
                <div className="indent-0">
                  4.  业界小班课程共计：20次
                </div>
                <div className="indent-0">
                  5.  1v1就职指导面谈/1v1就职模拟面试共计：30次
                </div>

                <div className="mt-5 indent-0">
                  （四）附加课程内容（需额外付费）：
                </div>
                <div className="indent-0">
                  代码测试服务（按次收费，每次10000日元）
                </div>
                <div className="mt-6 font-bold text-lg indent-0">
                  【15个月课程】
                </div>
                <div className="indent-0">
                  （一）基础套餐课程：
                </div>
                <div className="indent-0">
                  1.  日本就职相关讲义课程
                </div>
                <div className="indent-0">
                  2.  日本就职网测辅导书类资源（合同期满/解除后，使用权限关闭）
                </div>
                <div className="indent-0">
                  3.  大班课程共计：4次
                </div>
                <div className="indent-0">
                  4.  无业界小班课程
                </div>
                <div className="indent-0">
                  5.  1v1就职指导面谈/1v1就职模拟面试共计：15次
                </div>
                <div className="mt-5 indent-0">
                  （二）标准套餐课程：
                </div>
                <div className="indent-0">
                  1.  日本就职相关讲义课程
                </div>
                <div className="indent-0">
                  2.  日本就职网测辅导书类资源（合同期满/解除后，使用权限关闭）
                </div>
                <div className="indent-0">
                  3.  大班课程共计：4次
                </div>
                <div className="indent-0">
                  4.  业界小班课程共计：15次
                </div>
                <div className="indent-0">
                  5.  1v1就职指导面谈/1v1就职模拟面试共计：30次
                </div>
                <div className="mt-5 indent-0">
                  （三）升级套餐课程：
                </div>
                <div className="indent-0">
                  1.  日本就职相关讲义课程
                </div>
                <div className="indent-0">
                  2.  日本就职网测辅导书类资源（合同期满/解除后，使用权限关闭）
                </div>
                <div className="indent-0">
                  3.  大班课程共计：4次
                </div>
                <div className="indent-0">
                  4.  业界小班课程共计：30次
                </div>
                <div className="indent-0">
                  5.  1v1就职指导面谈/1v1就职模拟面试共计：45次
                </div>


                <div></div>
              </div>
            </div>
          </div>

          <div
            id="page14"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·学部</span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-0">
                <div className="indent-0">
                  （四）附加课程内容（需额外付费）：
                </div>
                <div className="indent-0">
                  代码测试服务（按次收费，每次10000日元）
                </div>
                <div className="mt-6 font-bold text-lg indent-0">
                  六、甲方的义务
                </div>
                <div>
                  1.	甲方承诺向乙方提供真实的日本就职信息，公司信息，选考信息等；
                </div>
                <div>
                  2.	甲方根据乙方的背景，服务需求，就职目标，为乙方规划合理的就职方案，协助完成乙方在日本就职的选考过程，并提供合同约定的日本就职相关服务；
                </div>
                <div>
                  3.	甲方应尊重乙方的公司选择意愿，指导和协助乙方完成日本就职选考的过程；
                </div>
                <div>
                  4.	甲方需指导和帮助乙方在选考过程中ES等书类的准备；
                </div>
                <div>
                  5.	甲方向乙方提供全面性日本就职相关的讲义课程（例：甲方需要提供公司业界分享/ES写法讲解等）；
                </div>
                <div>
                  6.	甲方有义务向乙方提供就职相关的面试指导服务；
                </div>
                <div>
                  7.	甲方有义务向乙方提供个人面谈服务，介绍日本就职选考基本内容。
                </div>
                <div className="mt-6 font-bold text-lg indent-0">
                  七、甲方的权利
                </div>
                <div>
                  1.   甲方有权及时了解乙方的就职选考进度，就职状况等信息，以确保为乙方提供精准的就职服务以及帮助；

                </div>
                <div>
                  2.   如遇特殊情况或乙方需求变化，甲方有权对服务内容进行合理调整，但应提前通知乙方并取得同意；
                </div>
                <div>
                  3   甲方有权将乙方的申请信息用于非公开性的内部研究和讨论。在隐藏申请人个人信息后，甲方可将乙方的申请信息用于公开宣传。
                </div>
                <div className="mt-6 font-bold text-lg indent-0">
                  八、乙方的义务
                </div>
                <div>
                  1.  乙方须确认已清楚本合同中所列条款，并保证甲方所提交的个人资料信息的真实性、有效性、合法性；
                </div>
                <div>
                  2.  乙方须及时向甲方缴纳合同约定的服务费用；
                </div>
                <div>
                 3.  乙方应积极配合甲方的服务工作，遵守面试等活动的安排；
                </div>
                <div>
                  4.  乙方应当尊重甲方的知识产权，不得擅自对课程进行录音、录像。对于甲方拥有知识产权的纸质版、电子版材料或者课件，乙方除在正常学习过程中合理使用外，不得私自复制、散发、销售，不得通过互联网进行分享、扩散和传播；
                </div>
                <div>
                  5.  甲方向乙方提供的个人面谈和个人面试原则上采取预约制。如乙方的日程发生变更，应及时与甲方进行调整联系。
                </div>
                <div className="mt-6 font-bold text-lg indent-0">
                  九、乙方的权利
                </div>
                <div>1.   乙方有权利在就职选考过程中如有任何疑问或问题，有权向甲方寻求帮助和支持；</div>
                <div>2.   乙方有权获得甲方提供的就职选考信息和帮忙服务，并在合理范围内提出意见和建议；</div>
                <div>3.   乙方对甲方咨询老师及授课老师的工作享有建议权、投诉权及监督权。</div>
                <div className="mt-6 font-bold text-lg indent-0">
                  十、续约解约
                </div>
                <div className='font-bold'>1.   本课程套餐不提供退费服务。</div>
                <div>2.   课程套餐的延长手续办理期限为乙方首次申请课程的使用结束日后（课程时长按12个月计算，每个月按30天计算）。{renderStrong('课程有效期到期后，需重新申请')}。</div>
                <div></div>
              </div>
            </div>
          </div>

          <div
            id="page15"
            className="border border-solid border-black m-5"
            style={{ width: 1050, height: 1485 }}
          >
            <div className="px-32 py-20 font-light">
              <div className="text-right">
                <span className="underline italic">新领域理工塾·学部</span>
                <div className="render-joint-icon"></div>
              </div>
              <div className="mt-20 leading-8 indent-0">
                <div>3.   {renderStrong('1v1课程单次延长费用：')}7500日元/小时（合同期满/解除后，使用权限关闭）</div>
                <div>4.   出现下列情况时，本协议自动解除，且不产生任何退费</div>
                <div className='indent-4'>1）乙方违反中日两国国家法律， 被追究刑事责任，或受到遣送回国处分等原因不能继续在日；</div>
                <div className='indent-4'>2）乙方在签订本协议或就职选考时提交虚假信息；</div>
                <div className='indent-4'>3）出现地震、火灾、洪水、战争等不可抗力因素时致甲方无能力向乙方提供服务时；</div>
                <div className='indent-4'>4）出现政治动乱、罢工等不可抗力因素时致甲方无能力向乙方无能力提供服务时。</div>
                <div>5.   有关不可抗力因素的补充：</div>
                <div className='pl-4'>如新冠疫情，政治原因或地震，台风，战争，以及其他不可预见并且对其发生和后果不能防止和避免的不可抗力直接影响本合同的履行或者不能按约定的条件履行时，遇有上述不可抗力的甲方应及时通知乙方，按其对本合同的影响程度，由双方协商决定是否解除合同免除或者部分免除履行合同的责任，或者延期履行合同，对于不可抗力造成的影响，甲乙双方应及时协商解决办法和补救措施，尽力采取合理措施，减轻可能给对方造成的损失并承担相应的赔偿责任。</div>



                <div className="mt-6 font-bold text-lg indent-0">十一、声明</div>
                <div className="indent-0">    1.甲方将按照乙方的需求和指导提供辅助就职服务，但不对乙方最终就业结果作出保证。</div>
                <div>2.在法律允许的范围内，甲方不承担因乙方使用服务或无法使用服务而导致的任何直接、间接、偶然、特殊或后续性损失或损害的责任。</div>

                  <div className="mt-6 font-bold text-lg indent-0">十二、争议解决</div>
                  <div className='pl-4'>本合同在履行过程中发生争议，双方可协商解决，协商不成的，双方可依法向上海仲裁委员会申请仲裁。</div>


                  <div className="mt-6 font-bold text-lg indent-0">十三、补充条款</div>
                  <div className='pl-4'>本合同未尽事宜，由下列补充条款进行约定。{renderStrong('补充条款与本合同其他条款不一致的，以补充条款为准。')}</div>
                <div className="mt-20 font-bold text-lg indent-0">
                  十四、合同生效
                </div>
                <div className='pl-4'>本合同自甲方盖章乙方签署之日起生效。
                  合同正本连同补充条款共五页，{renderStrong('一式两份')}
                  ，甲乙双方各执一份，各份具有{renderStrong('同等')}法律效力。
                </div>
              </div>
              <div className="mt-20 flex">
                <div className="w-1/2">
                  <div className="font-bold">甲方（盖章）：</div>
                  <div className="mt-10 font-bold">新领域理工塾</div>
                  <div className="font-bold">SHINRYOIKIRIKOJUKU</div>
                  <div className="mt-10 font-bold">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;日
                  </div>
                </div>
                <div className="w-1/2">
                  <div className="font-bold">乙方（签字）：</div>
                  <div className="mt-16 font-bold">乙方（监护人签字）：</div>
                  <div className="mt-10 font-bold">
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;年&nbsp;&nbsp;&nbsp;月&nbsp;&nbsp;&nbsp;日
                  </div>
                </div>
              </div>

              </div>
            </div>
          </div>
      </Modal>
      <Button
        size="small"
        loading={genContractNoApi.loading}
        onClick={handleOpen}
      >
        就职合同
      </Button>
    </>
  );
};

export default RenderJoint;
