import { useParams } from 'umi';
import { useRequest } from 'ahooks';
import { Button, message, Modal, Table, Space, Typography } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
////
import { useForm } from '@/hooks/useSKForm';
import {
  getStudentById,
  addCourseToStudent,
  updateCourseToStudent,
  removeCourseFromStudent,
} from '@/services/request/student';
import { formatterEn } from '@/utils/function';
import { renderDate } from '@/hooks/useDayjs';
import { useUserOptions } from '@/hooks/useApiOptions';
import StudentCourseForm from '@/pages/student/components/StudentCourseForm';
import RenderJointFaculty from './RenderJointFaculty';
import RenderJointWenkeFaculty from './RenderJointWenkeFaculty';
import RenderJointGradWenke from './RenderJointGradWenke';
import RenderJointGrad from './RenderJointGrad';
import RenderJointTopic from './RenderJointTopic';
import RenderJointEmployment from './RenderJointEmployment';
import { useAccess, Access, renderAccess } from '@/hooks/useAccess';

export interface StudentApplicationProps {}

const StudentApplication: React.FC<StudentApplicationProps> = (props) => {
  // state
  const access = useAccess();
  const { formType, formProps, handleOpen } = useForm<API.Course>();
  const { studentId = '' } = useParams<any>();
  const { renderUserLabel } = useUserOptions();

  // api
  const studentAPI = useRequest(
    async () => await getStudentById({ studentId }),
    { cacheKey: 'studentById' },
  );

  // action
  const handleAdd = () => {
    handleOpen({
      title: '新建报名课程',
      type: 'add',
      data: studentAPI.data,
    });
  };
  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await addCourseToStudent({ studentId, ...v });
      }
      if (formType === 'edit') {
        if (!formProps.dataSource?._id) throw '';
        await updateCourseToStudent({
          studentId,
          courseId: formProps.dataSource?._id,
          course: v,
        });
      }
      await studentAPI.refreshAsync();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  // render
  const RenderStudentCoursesActions = renderAccess({
    accessible:
      access.page?.['studentCourses.JOINT'] ||
      access.page?.['studentCourses.D'] ||
      access.page?.['studentCourses.U'],
    children: (
      <Table.ColumnGroup title="操作">
        {renderAccess({
          accessible: access.page?.['studentCourses.JOINT'],
          children: (
            <Table.Column
              fixed="right"
              title="生成合同"
              width={300}
              render={(row) => (
                <Space wrap size="small">
                  <RenderJointFaculty
                    dataSource={{ data: studentAPI.data, row }}
                  />
                  <RenderJointWenkeFaculty
                    dataSource={{ data: studentAPI.data, row }}
                  />
                  <RenderJointGrad
                    dataSource={{ data: studentAPI.data, row }}
                  />
                  <RenderJointTopic
                    dataSource={{ data: studentAPI.data, row }}
                  />
                  <RenderJointGradWenke
                    dataSource={{ data: studentAPI.data, row }}
                  />
                  <RenderJointEmployment
                    dataSource={{ data: studentAPI.data, row }}
                  />
                </Space>
              )}
            />
          ),
        })}
        {renderAccess({
          accessible: access.page?.['studentCourses.U'],
          children: (
            <Table.Column
              title="编辑"
              fixed="right"
              width={50}
              render={(row) => {
                const handleEdit = () => {
                  handleOpen({
                    title: '编辑报名课程',
                    type: 'edit',
                    data: row,
                  });
                };
                return (
                  <Button size="small" onClick={handleEdit}>
                    <EditOutlined />
                  </Button>
                );
              }}
            />
          ),
        })}
        {renderAccess({
          accessible: access.page?.['studentCourses.D'],
          children: (
            <Table.Column
              title="删除"
              fixed="right"
              width={50}
              render={(row) => {
                const handleDelete = () => {
                  Modal.confirm({
                    centered: true,
                    title: '删除',
                    content: '确认删除本报名课程，删除后无法恢复。',
                    onOk: async () => {
                      await removeCourseFromStudent({
                        studentId,
                        courseId: row?._id,
                      });
                      await studentAPI.refreshAsync();
                    },
                    okText: '确认',
                    cancelText: '取消',
                  });
                };
                return (
                  <Button size="small" onClick={handleDelete}>
                    <DeleteOutlined />
                  </Button>
                );
              }}
            />
          ),
        })}
      </Table.ColumnGroup>
    ),
  });

  return (
    <>
      <StudentCourseForm
        type={formType}
        {...formProps}
        onSubmit={handleSubmit}
      />
      <Access accessible={access.page?.['studentCourses.C']}>
        <Button className="mb-4" type="primary" onClick={handleAdd}>
          新建
        </Button>
      </Access>
      <Table
        rowKey="_id"
        dataSource={studentAPI?.data?.courses}
        size="small"
        scroll={{ x: 1600 }}
        expandable={{
          expandedRowRender: (row) => (
            <>
              <Typography.Title level={3}>联络群信息</Typography.Title>
              <Typography.Paragraph style={{ whiteSpace: 'pre-line' }} copyable>
                {`学生姓名：${studentAPI?.data?.name}
微信号：${studentAPI?.data?.wechat}
课程名：${row?.course_name || ''}
课程金额：${formatterEn(row?.original_price)}
优惠金额：${formatterEn(row?.off_price)}
实缴金额：${formatterEn(row?.final_price)}
支付方式：
销售老师：${renderUserLabel(studentAPI?.data?.sale_teacher1)}
来源：${row?.custom_source || ''}
课程有效期：${renderDate('YYYY.MM.DD')(row?.expired_date)}
备注：${row?.other_note || ''}
经办人：${renderUserLabel(row?.accept_teacher)}`}
              </Typography.Paragraph>
            </>
          ),
        }}
      >
        <Table.Column
          fixed="left"
          title="课程有效期"
          dataIndex="expired_date"
          render={renderDate()}
        />
        <Table.Column fixed="left" title="学生来源" dataIndex="custom_source" />
        <Table.Column title="报名院校" dataIndex="department_name" />
        <Table.Column title="报名课程" dataIndex="course_name" />
        <Table.Column title="vip课程" dataIndex="vip_course" />
        <Table.Column title="数学课程" dataIndex="math_course_note" />
        <Table.Column title="英语课程" dataIndex="en_course_note" />
        <Table.Column title="日语课程" dataIndex="ja_course_note" />
        <Table.Column
          title="课程金额"
          dataIndex="original_price"
          render={(v) => formatterEn(v)}
        />
        <Table.Column
          title="优惠金额"
          dataIndex="off_price"
          render={(v) => formatterEn(v)}
        />
        <Table.Column
          title="实缴金额"
          dataIndex="final_price"
          render={(v) => formatterEn(v)}
        />
        <Table.Column title="其他备注" dataIndex="other_note" />
        {RenderStudentCoursesActions}
      </Table>
    </>
  );
};

export default StudentApplication;
