import { useRequest } from 'ahooks';
import { useState } from 'react';
import { Badge, Calendar, Card, Col, List, Row, Select, Tag } from 'antd';
////
import { dayjs } from '@/hooks/useDayjs';
import { courseJSON } from '@/hooks/useJSON';
import { useUserOptions } from '@/hooks/useApiOptions';
import { getAllCourseEvents } from '@/services/request/courseEvents';
import { useSearchParams } from 'umi';

export interface TeachingResearchProps {}

const TeachingResearch: React.FC<TeachingResearchProps> = () => {
  // state
  const [searchParams] = useSearchParams();
  const [date, setDate] = useState(dayjs());
  const { renderUserLabel } = useUserOptions();

  // Helper function to format category parameter
  const formatCategoryParam = () => {
    // Try both 'categories' and 'category' parameter names for compatibility
    const categoriesParam = searchParams.get('categories') || searchParams.get('category');
    console.log('Raw category param:', categoriesParam);

    if (!categoriesParam) return undefined;

    try {
      // Try to parse as JSON first (for array format)
      const parsed = JSON.parse(categoriesParam);
      console.log('Parsed category:', parsed);

      // Check if it's already in the correct format (array of arrays)
      if (Array.isArray(parsed) && parsed.length > 0 && Array.isArray(parsed[0])) {
        console.log('Category already in correct format (string[][]):', parsed);
        return parsed;
      }

      // If it's a single array, wrap it in another array (like in list.tsx)
      if (Array.isArray(parsed)) {
        console.log('Converting single array to array of arrays:', [parsed]);
        return [parsed];
      }

      return parsed;
    } catch (error) {
      console.log('JSON parse failed, treating as comma-separated string:', error);
      // If not JSON, treat as comma-separated string and convert to array format
      const categoryArray = categoriesParam.split(',').map(cat => cat.trim()).filter(Boolean);
      const result = categoryArray.length > 0 ? [categoryArray] : undefined;
      console.log('Comma-separated result:', result);
      return result;
    }
  };

  // api
  const monthApi = useRequest(
    async () => {
      const categoryParam = formatCategoryParam();
      const apiParams = {
        sortField: 'date',
        sortOrder: 1 as 1,
        perPage: 1000, // Set high limit to fetch all events in 3-month range
        category: categoryParam,
        date_start: date.startOf('M').add(-1, 'M').format('YYYY-MM-DD'),
        date_end: date.endOf('M').add(1, 'M').format('YYYY-MM-DD'),
      };
      console.log('Month API params:', apiParams);
      const result = await getAllCourseEvents(apiParams);
      console.log('Month API result:', result);
      return result;
    },
    { refreshDeps: [searchParams.get('categories'), searchParams.get('category')] },
  );

  const dateApi = useRequest(
    async () => {
      const categoryParam = formatCategoryParam();
      const apiParams = {
        sortField: 'start_date_str',
        sortOrder: 1 as 1,
        category: categoryParam,
        date_start: date.startOf('D').format('YYYY-MM-DD'),
        date_end: date.endOf('D').format('YYYY-MM-DD'),
      };
      console.log('Date API params:', apiParams);
      const result = await getAllCourseEvents(apiParams);
      console.log('Date API result:', result);
      return result;
    },
    { refreshDeps: [date, searchParams.get('categories'), searchParams.get('category')] },
  );

  // Helper function to get course events for a specific date
  const getListData = (value: any) => {

    const dayEvents = monthApi?.data?.courseEvents?.filter((item: any) => {
      const matches = item?.date?.match(value.format('YYYY.MM.DD'));

      return matches;
    }) || [];


    return dayEvents.map((event: any) => {
      // Map course status to Badge status types
      let badgeStatus: 'success' | 'processing' | 'error' | 'warning' | 'default' = 'default';

      switch (event.status) {
        case 0: // 未开始
          badgeStatus = 'processing';
          break;
        case 1: // 已完成
          badgeStatus = 'success';
          break;
        case 2: // 已缺席
          badgeStatus = 'error';
          break;
        default:
          badgeStatus = 'default';
      }

      // Truncate long course names - increased length for better readability
      const truncateName = (name: string, maxLength: number = 16) => {
        if (!name) return '未命名课程';
        if (name.length <= maxLength) return name;
        return name.substring(0, maxLength) + '...';
      };

      return {
        type: badgeStatus,
        content: truncateName(event.name),
        time: `${event.start_date_str}-${event.end_date_str}`,
        id: event._id
      };
    });
  };

  // Date cell render function with improved layout
  const dateCellRender = (value: any) => {
    const listData = getListData(value);

    // If no events, return null to show nothing
    if (listData.length === 0) {
      return null;
    }

    return (
      <div style={{
        position: 'relative',
        height: '100%',
        paddingTop: '20px'
      }}>
        <ul className="events" style={{
          listStyle: 'none',
          padding: 0,
          margin: 0,
          textAlign: 'left',
          overflow: 'hidden'
        }}>
          {listData.slice(0, 4).map((item: any, index: number) => (
            <li key={item.id || index} style={{
              marginBottom: '3px',
              fontSize: '11px',
              lineHeight: '16px'
            }}>
              <Badge
                status={item.type}
                text={item.content}
              />
            </li>
          ))}
          {listData.length > 4 && (
            <li style={{
              fontSize: '10px',
              color: '#666',
              textAlign: 'center',
              marginTop: '4px',
              fontStyle: 'italic'
            }}>
              +{listData.length - 4}更多
            </li>
          )}
        </ul>
      </div>
    );
  };

  return (
    <>
      <Card title="新领域课程表" size="small">
        <style>
          {`
            /* Increase calendar cell height and customize layout */
            .ant-picker-calendar-mini .ant-picker-cell {
              height: 120px !important;
              position: relative;
              padding: 4px !important;
            }

            /* Position date number in top-right corner */
            .ant-picker-calendar-mini .ant-picker-cell-inner {
              position: absolute;
              top: 2px;
              right: 4px;
              width: auto !important;
              height: auto !important;
              line-height: 1 !important;
              font-size: 11px !important;
              font-weight: 500;
              color: #666;
              background: rgba(255, 255, 255, 0.8);
              border-radius: 2px;
              padding: 1px 3px;
              z-index: 2;
            }

            /* Style for selected/today date number */
            .ant-picker-cell-selected .ant-picker-cell-inner,
            .ant-picker-cell-today .ant-picker-cell-inner {
              background: rgba(24, 144, 255, 0.1) !important;
              color: #1890ff !important;
              font-weight: 600;
            }

            /* Ensure course content has proper spacing */
            .ant-picker-calendar-mini .ant-picker-cell .events {
              margin-top: 20px;
            }

            /* Hover effects for better interactivity */
            .ant-picker-cell:hover .ant-picker-cell-inner {
              background: rgba(24, 144, 255, 0.2) !important;
              color: #1890ff !important;
            }

            /* Ensure proper spacing in calendar grid */
            .ant-picker-calendar-mini .ant-picker-content {
              height: auto;
            }

            .ant-picker-calendar-mini .ant-picker-content th,
            .ant-picker-calendar-mini .ant-picker-content td {
              border-right: 1px solid #f0f0f0;
              border-bottom: 1px solid #f0f0f0;
            }

            /* Responsive adjustments for smaller screens */
            @media (max-width: 768px) {
              .ant-picker-calendar-mini .ant-picker-cell {
                height: 100px !important;
              }

              .ant-picker-calendar-mini .ant-picker-cell .events {
                margin-top: 18px;
              }

              .ant-picker-calendar-mini .ant-picker-cell .events li {
                font-size: 10px !important;
                line-height: 14px !important;
              }

              .ant-picker-calendar-mini .ant-picker-cell .events li span {
                max-width: 80px !important;
              }
            }
          `}
        </style>
        <Calendar
          style={{ width: '100%' }}
          fullscreen={false}
          value={date}
          onSelect={setDate}
          onPanelChange={monthApi.runAsync}
          headerRender={({ value, onChange }) => {
            const start = 0;
            const end = 12;
            const monthOptions = [];

            let current = value.clone();
            const localeData = value.localeData();
            const months = [];
            for (let i = 0; i < 12; i++) {
              current = current.month(i);
              months.push(localeData.monthsShort(current));
            }

            for (let i = start; i < end; i++) {
              monthOptions.push(
                <Select.Option key={i} value={i} className="month-item">
                  {months[i]}
                </Select.Option>,
              );
            }

            const year = value.year();
            const month = value.month();
            const options = [];
            for (let i = year - 10; i < year + 10; i += 1) {
              options.push(
                <Select.Option key={i} value={i} className="year-item">
                  {i}
                </Select.Option>,
              );
            }

            return (
              <Row gutter={8} justify="end" className="mb-2">
                <Col>
                  <Select
                    size="small"
                    dropdownMatchSelectWidth={false}
                    value={year}
                    onChange={(newYear) => {
                      const now = value.clone().year(newYear);
                      onChange(now);
                    }}
                  >
                    {options}
                  </Select>
                </Col>
                <Col>
                  <Select
                    size="small"
                    dropdownMatchSelectWidth={false}
                    value={month}
                    onChange={(newMonth) => {
                      const now = value.clone().month(newMonth);
                      onChange(now);
                    }}
                  >
                    {monthOptions}
                  </Select>
                </Col>
              </Row>
            );
          }}
          dateCellRender={dateCellRender}
        />
      </Card>
      <Card
        size="small"
        title={date?.format(
          `YYYY年MM月DD日 课程 ${dateApi?.data?.courseEvents?.length} 门`,
        )}
      >
        <List<API.CourseEvent>
          size="small"
          dataSource={dateApi?.data?.courseEvents}
          renderItem={(item) => {
            const status = courseJSON.Status?.[item?.status];
            return (
              <List.Item
                extra={<Tag color={status?.color}>{status?.label}</Tag>}
              >
                <div>
                  <div className="text-base font-medium mb-1">{item?.name}</div>
                  <div className="text-xs text-neutral-600">
                    <span className="text-neutral-400">上课时间：</span>
                    {item?.start_date_str} ~ {item?.end_date_str}
                  </div>
                  <div className="text-xs text-neutral-600">
                    <span className="text-neutral-400">课程属性：</span>
                    {item?.category?.map((cat: string[]) => cat.join('-')).join(', ')}
                  </div>
                  <div className="text-xs text-neutral-600">
                    <span className="text-neutral-400">上课老师：</span>
                    {renderUserLabel(item?.teacher)}
                  </div>
                </div>
              </List.Item>
            );
          }}
        />
      </Card>
    </>
  );
};

export default TeachingResearch;
