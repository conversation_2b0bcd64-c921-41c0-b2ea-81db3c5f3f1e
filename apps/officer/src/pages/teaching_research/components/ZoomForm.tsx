import { useEffect } from 'react';
import { Modal, Form, Input, Select } from 'antd';
////
import { useFormBasic } from '@/hooks/useSKForm';
import type { SKFormProps } from '@/hooks/useSKForm';

const formItemLayout = {
  labelCol: { span: 0 },
  wrapperCol: { span: 24 },
};

interface DataSource extends API.ZoomSetting {}

// Category options for Zoom accounts
const categoryOptions = [
  { value: '大学院', label: '大学院' },
  { value: '学部', label: '学部' },
  { value: '数学', label: '数学' },
  { value: '语言', label: '语言' },
  { value: '其他', label: '其他' },
];

export interface ZoomFormProps extends SKFormProps<DataSource> {}

const ZoomForm: React.FC<ZoomFormProps> = (props) => {
  // state
  const { modalProps, formProps } = useFormBasic(props);

  // init
  useEffect(() => {
    if (props?.visible) {
      formProps?.form?.setFieldsValue(props?.dataSource);
    }
  }, [props]);

  return (
    <Modal {...modalProps}>
      <Form
        name="ZoomForm"
        labelAlign="left"
        {...formItemLayout}
        {...formProps}
      >
        <Form.Item label="账号名称" name="account">
          <Input />
        </Form.Item>
        <Form.Item label="类别" name="category">
          <Select
            placeholder="选择类别"
            options={categoryOptions}
            allowClear
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default ZoomForm;
