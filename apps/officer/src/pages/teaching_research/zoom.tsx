import { useAntdTable } from 'ahooks';
import {
  Form,
  Input,
  Table,
  Row,
  Col,
  Button,
  Modal,
  Space,
  message,
  Select,
} from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
////
import { fixParams } from '@/utils/function';
import {
  createZoomSetting,
  deleteZoomSettingById,
  getAllZoomSettings,
  updateZoomSetting,
} from '@/services/request/zoomSettings';
import ZoomForm from './components/ZoomForm';
import { useForm } from '@/hooks/useSKForm';

export interface ZoomSettingsProps {}

// Category options for Zoom accounts
const categoryOptions = [
  { value: '大学院', label: '大学院' },
  { value: '学部', label: '学部' },
  { value: '数学', label: '数学' },
  { value: '语言', label: '语言' },
  { value: '其他', label: '其他' },
];

const ZoomSettings: React.FC<ZoomSettingsProps> = () => {
  // state
  const [form] = Form.useForm();
  const { formType, formProps, handleOpen } = useForm<API.ZoomSetting>();

  // api
  const getTableData = async (_: any, formData: any) => {
    const data = await getAllZoomSettings({
      ...fixParams(false, formData),
    });
    return {
      total: data?.totalCount,
      list: (data?.zoomSettings as API.ZoomSetting[]) || [],
    };
  };
  const zoomAccountAPI = useAntdTable(getTableData, { form });

  // action
  const handleAdd = () => {
    handleOpen({
      title: '新建账号',
      type: 'add',
      data: null,
    });
  };

  const handleSubmit = async (v: any) => {
    try {
      if (formType === 'add') {
        await createZoomSetting({
          ...v,
        });
      } else if (formType === 'edit') {
        if (formProps?.dataSource?._id) {
          await updateZoomSetting({
            ...v,
            zoomId: formProps?.dataSource?._id,
          });
        }
      }
      zoomAccountAPI.refresh();
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Field:', error);
    }
  };

  // Handle category update
  const handleCategoryChange = async (record: API.ZoomSetting, category: string) => {
    try {
      await updateZoomSetting({
        category,
        zoomId: record._id,
      });
      zoomAccountAPI.refresh();
      message.success('类别更新成功');
    } catch (error: any) {
      message.warning(error?.message);
      console.log('Category update error:', error);
    }
  };

  return (
    <>
      <ZoomForm type={formType} {...formProps} onSubmit={handleSubmit} />
      <Form form={form}>
        <Row justify="end" gutter={16}>
          <Col flex="150px">
            <Form.Item name="account_lk">
              <Input placeholder="账号名称" />
            </Form.Item>
          </Col>
          <Col>
            <Form.Item>
              <Space>
                <Button type="primary" onClick={zoomAccountAPI.search.submit}>
                  搜索
                </Button>
                <Button onClick={zoomAccountAPI.search.reset}>重置</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Button className="mb-4" type="primary" onClick={handleAdd}>
        新建
      </Button>
      <Table
        rowKey="_id"
        size="small"
        {...zoomAccountAPI.tableProps}
        pagination={{
          ...zoomAccountAPI.tableProps.pagination,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 件`,
        }}
        scroll={{ y: 'calc( 100vh - 360px )' }}
      >
        <Table.Column sorter title="账号名称" dataIndex="account" />
        <Table.Column
          title="类别"
          dataIndex="category"
          width={120}
          render={(category: string, record: API.ZoomSetting) => (
            <Select
              value={category}
              placeholder="选择类别"
              style={{ width: '100%' }}
              options={categoryOptions}
              onChange={(value) => handleCategoryChange(record, value)}
            />
          )}
        />
        <Table.ColumnGroup title="操作">
          <Table.Column
            title="编辑"
            fixed="right"
            width={50}
            render={(row) => {
              const handleEdit = () => {
                handleOpen({
                  title: '编辑账号',
                  type: 'edit',
                  data: row,
                });
              };
              return (
                <Button
                  size="small"
                  type="primary"
                  ghost
                  disabled={row?.status === 1}
                  onClick={handleEdit}
                >
                  <EditOutlined />
                </Button>
              );
            }}
          />
          <Table.Column
            title="删除"
            fixed="right"
            width={50}
            render={(row) => {
              const handleDelete = () => {
                Modal.confirm({
                  centered: true,
                  title: '删除',
                  content: '确认删除本ZOOM账号，删除后无法恢复。',
                  onOk: async () => {
                    await deleteZoomSettingById({
                      zoomId: row?._id,
                    });
                    zoomAccountAPI.search.submit();
                  },
                  okText: '确认',
                  cancelText: '取消',
                });
              };
              return (
                <Button danger size="small" onClick={handleDelete}>
                  <DeleteOutlined />
                </Button>
              );
            }}
          />
        </Table.ColumnGroup>
      </Table>
    </>
  );
};

export default ZoomSettings;
